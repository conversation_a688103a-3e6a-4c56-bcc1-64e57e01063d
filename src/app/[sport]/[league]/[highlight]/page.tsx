import styles from '@/app/page.module.css'
import Highlight from '@/components/Highlight/Highlight'
import HighlightLoading from '@/components/Highlight/HighlightLoading'
import {Leagues} from '@/components/Leagues/Leagues'
import StructuredData from '@/components/SEO/StructuredData'
import {leagues} from '@/types/league'
import {HighlightsCache} from '@/utils/cache'
import {HIGHLIGHTDESCRIPTION} from '@/utils/metadata'
import {supabase} from '@/utils/supabaseClient'
import {Metadata} from 'next'
import {Suspense} from 'react'

export async function generateMetadata({
    params,
}: {
    params: Promise<{sport: string; league: string; highlight: string}>
}): Promise<Metadata> {
    const {sport, highlight} = await params

    // Decode the URL-encoded highlight parameter
    const decodedHighlight = decodeURIComponent(highlight)

    // Try to get cached highlight data first
    let data = await HighlightsCache.getHighlight(decodedHighlight)

    if (!data || data.length === 0) {
        // Cache miss - fetch from database
        const result = await supabase
            .from('highlights')
            .select(
                'id, name, label, duration, thumbnail, video_url, sport, season, created_at, league, country'
            )
            .eq('label', decodedHighlight)
            .single()
        data = result.data ? [result.data] : null

        // Cache the result if found
        if (data) {
            await HighlightsCache.setHighlight(decodedHighlight, data)
        }
    }

    const highlightData = data && data.length > 0 ? data[0] : null

    if (highlightData) {
        // Get league display name
        const sportLeagues = leagues[sport] || []
        const leagueInfo = sportLeagues.find((l) => l.label === highlightData.league)
        const leagueDisplayName = leagueInfo?.displayName || highlightData.league
        const title = `${highlightData.name} - ${leagueDisplayName} ${highlightData.season}`
        const description = HIGHLIGHTDESCRIPTION(
            highlightData.name,
            leagueDisplayName,
            highlightData.season
        )

        return {
            title: `${title} - Thepelota`,
            description,
            alternates: {
                canonical: `https://thepelota.tv/${sport}/${highlightData.league}/${highlight}`,
            },
            openGraph: {
                title: `${title} - Thepelota`,
                description,
                url: `https://thepelota.tv/${sport}/${highlightData.league}/${highlight}`,
                siteName: 'Thepelota',
                type: 'video.other',
                images: [
                    {
                        url: highlightData.thumbnail || `https://thepelota.tv/og-image.jpg`,
                        width: 1200,
                        height: 630,
                        alt: `${title} - Thepelota`,
                    },
                ],
                videos: [
                    {
                        url: highlightData.video_url,
                        type: 'video/mp4',
                        width: 1280,
                        height: 720,
                    },
                ],
            },
            twitter: {
                card: 'player',
                title: `${title} - Thepelota`,
                description,
                images: [highlightData.thumbnail || `https://thepelota.tv/og-image.jpg`],
                players: [
                    {
                        playerUrl: highlightData.video_url,
                        streamUrl: highlightData.video_url,
                        width: 1280,
                        height: 720,
                    },
                ],
            },
        }
    }

    // Fallback if highlight not found
    return {
        title: 'Highlight not found',
        description: 'The requested highlight could not be found.',
    }
}

export default async function Home({
    params,
}: {
    params: Promise<{sport: string; league: string; highlight: string}>
}) {
    const {sport, league, highlight} = await params

    // Decode the URL-encoded highlight parameter
    const decodedHighlight = decodeURIComponent(highlight)

    // Try to get cached highlight data for structured data
    let structuredHighlightData = await HighlightsCache.getHighlight(decodedHighlight)

    if (!structuredHighlightData || structuredHighlightData.length === 0) {
        // Cache miss - fetch from database
        const result = await supabase
            .from('highlights')
            .select(
                'id, name, label, duration, thumbnail, video_url, sport, season, created_at, league, country'
            )
            .eq('label', decodedHighlight)
            .single()

        structuredHighlightData = result.data ? [result.data] : null

        // Cache the result if found
        if (structuredHighlightData) {
            await HighlightsCache.setHighlight(decodedHighlight, structuredHighlightData)
        }
    }

    const highlightDataForStructured =
        structuredHighlightData && structuredHighlightData.length > 0
            ? structuredHighlightData[0]
            : null

    // Get league info for breadcrumbs
    const sportLeagues = leagues[sport] || []
    const leagueInfo = sportLeagues.find((l) => l.label === league)

    const breadcrumbs = [
        {name: 'Home', url: 'https://thepelota.tv'},
        {
            name: sport.charAt(0).toUpperCase() + sport.slice(1),
            url: `https://thepelota.tv/${sport}`,
        },
        {name: leagueInfo?.displayName || league, url: `https://thepelota.tv/${sport}/${league}`},
        {
            name: highlightDataForStructured?.name || highlight,
            url: `https://thepelota.tv/${sport}/${league}/${highlight}`,
        },
    ]

    return (
        <div className={styles.pageContainer}>
            {highlightDataForStructured && (
                <>
                    <StructuredData
                        type='highlight'
                        data={{highlight: highlightDataForStructured}}
                    />
                    <StructuredData type='breadcrumb' data={{breadcrumbs}} />
                </>
            )}
            <aside>
                <Leagues />
            </aside>
            <Suspense fallback={<HighlightLoading />}>
                <Highlight />
            </Suspense>
        </div>
    )
}
