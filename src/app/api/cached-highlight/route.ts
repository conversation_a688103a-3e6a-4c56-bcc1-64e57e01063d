import {HighlightsCache} from '@/utils/cache'
import {supabase} from '@/utils/supabaseClient'
import {NextRequest, NextResponse} from 'next/server'

export async function GET(request: NextRequest) {
    try {
        const {searchParams} = new URL(request.url)
        const label = searchParams.get('label')

        if (!label) {
            return NextResponse.json({error: 'Label parameter is required'}, {status: 400})
        }

        // Next.js automatically decodes URL parameters, but our database stores encoded labels
        // So we need to re-encode the parameter to match what's in the database
        const encodedLabel = encodeURIComponent(label)

        // Try to get from cache first
        const cachedHighlight = await HighlightsCache.getHighlight(encodedLabel)

        if (cachedHighlight) {
            console.log(`Cache HIT for highlight: ${encodedLabel}`)
            return NextResponse.json(
                {
                    highlight: cachedHighlight,
                    cached: true,
                    timestamp: new Date().toISOString(),
                },
                {
                    headers: {
                        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
                        'X-Cache': 'HIT',
                    },
                }
            )
        }

        console.log(`Cache MISS for highlight: ${encodedLabel} - fetching from database`)

        // Cache miss - fetch from database
        const {data: highlight, error} = await supabase
            .from('highlights')
            .select(
                'id, name, label, duration, thumbnail, video_url, sport, season, created_at, league, country'
            )
            .eq('label', encodedLabel)
            .order('created_at', {ascending: false})

        if (error) {
            console.error('Database error:', error)
            return NextResponse.json({error: 'Failed to fetch highlight'}, {status: 500})
        }

        const highlightData = highlight || []

        // Cache the results
        await HighlightsCache.setHighlight(encodedLabel, highlightData)

        return NextResponse.json(
            {
                highlight: highlightData,
                cached: false,
                timestamp: new Date().toISOString(),
            },
            {
                headers: {
                    'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
                    'X-Cache': 'MISS',
                },
            }
        )
    } catch (error) {
        console.error('API error:', error)
        return NextResponse.json({error: 'Internal server error'}, {status: 500})
    }
}
