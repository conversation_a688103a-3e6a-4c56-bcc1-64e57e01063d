export function generateLabel(title: string) {
    return encodeURIComponent(
        title
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .trim()
            .toLowerCase()
            .replace(/\s+/g, '-')
    )
}

export function cleanYoutubeId(videoId: string) {
    return videoId.replace('-', '').replace('_', '')
}

/*
export function extractTeamsFromTitle(title: string): string[] {
    const teams = [
        // La Liga
        'FC Barcelona',
        'Real Madrid',
        'Atlético Madrid',
        'Athletic Club',
        'Villarreal CF',
        'Real Betis',
        'Rayo Vallecano',
        'RC Celta',
        'RCD Mallorca',
        'Real Sociedad',
        'Sevilla FC',
        'Getafe CF',
        'Girona FC',
        'CA Osasuna',
        'Valencia CF',
        'RCD Espanyol',
        'Deportivo Alavés',
        'CD Leganes',
        'UD Las Palmas',
        'Real Valladolid CF',

        // Premier League
        'Arsenal',
        'Aston Villa',
        'Brentford',
        'Brighton & Hove Albion',
        'Ipswich Town',
        'Chelsea',
        'Crystal Palace',
        'Everton',
        'Fulham',
        'Liverpool',
        'Leicester City,',
        'Manchester City',
        'Manchester United',
        'Newcastle United',
        'Nottingham Forest',
        'Southampton',
        'Tottenham Hotspur',
        'West Ham United',
        'Wolverhampton Wanderers',
        'Bournemouth',
        // Promotions
        'Leeds United',
        'Burnley',
        'Sunderland',

        // MLS
        'Atlanta United FC',
        'Austin FC',
        'Charlotte FC',
        'Chicago Fire FC',
        'FC Cincinnati',
        'Colorado Rapids',
        'Columbus Crew',
        'FC Dallas',
        'D.C. United',
        'Houston Dynamo FC',
        'Sporting Kansas City',
        'LA Galaxy',
        'Los Angeles FC',
        'Inter Miami CF',
        'Minnesota United FC',
        'CF Montréal',
        'Nashville SC',
        'New England Revolution',
        'New York City FC',
        'New York Red Bulls',
        'Orlando City SC',
        'Philadelphia Union',
        'Portland Timbers',
        'Real Salt Lake',
        'San Jose Earthquakes',
        'Seattle Sounders FC',
        'St. Louis CITY SC',
        'Toronto FC',
        'Vancouver Whitecaps FC',
        'San Diego FC',

        // Bundesliga
        'FC Bayern',
        'Bayer 04 Leverkusen',
        'Bayer Leverkusen',
        'Borussia Dortmund',
        'RB Leipzig',
        'Eintracht Frankfurt',
        'Freiburg',
        'Mainz',
        'VfB Stuttgart',
        'Werder Bremen',
        "Borussia M'gladbach",
        'VfL Wolfsburg',
        'Hoffenheim',
        'Union Berlin',
        'FC Augsburg',
        'VfL Bochum',
        'Heidenheim',
        'Holstein Kiel',
        'St. Pauli',

        // Ligue 1
        'PSG',
        'Marseille',
        'AS Monaco',
        'Olympique Lyon',
        'Strasbourg',
        'Lille',
        'Nice',
        'Brest',
        'Lens',
        'Rennes',
        'Auxerre',
        'Angers',
        'Montpellier',
        'Nantes',
        'Reims',
        'Saint Etienne',
        'Le Havre',
        'Toulouse',
        // Promotions
        'Lorient',
        'Paris FC',
        'Metz',

        // Serie A
        'Inter Milan',
        'Napoli',
        'Atalanta',
        'Juventus',
        'Bologna',
        'Roma',
        'Lazio',
        'Fiorentina',
        'AC Milan',
        'Torino',
        'Udinese',
        'Como',
        'Genoa',
        'Verona',
        'Cagliari',
        'Parma',
        'Lecce',
        'Venezia',
        'Empoli',
        'Monza',
        // Promotions
        'Sassuolo',
        'Pisa',
        'Cremonese',

        // Champions League additions
        'Benfica',
        'Club Brugge',
        'Feyenoord',
        'PSV',
        'Sporting',
        'Celtic',
        'Shakhtar',
        'Young Boys',
        'Dinamo',
        'Sturm',
        'Slovan',
        'Sparta Praha',
        'Salzburg',
        'Crvena zvezda',
        //TODO UPDATE

        // Europa League additions
        'Ajax',
        'AZ',
        'Twente',
        'Beşiktaş',
        'Fenerbahçe',
        'Anderlecht',
        'Union Saint-Gilloise',
        'Slavia Prague',
        'Viktoria Plzeň',
        'Nice',
        'Lyon',
        'Olympiacos',
        'Braga',
        'Porto',
        'Elfsborg',
        'Malmö',
        'Qarabağ',
        'Ludogorets',
        'Bodo/Glimt',
        'Bodø/Glimt',
        'Steaua Bucharest',
        'Viktoria Plzen',
        'Ferencváros',
        'Midtjylland',
        'PAOK',
        'FC Twente',
        'RFS',

        // Conference League additions
        'Chelsea',
        'Fiorentina',
        'Djurgårdens IF',
        'Rapid Wien',
        'LASK',
        'Copenhagen',
        'Gent',
        'İstanbul Başakşehir',
        'Molde',
        'Legia Warsaw',
        'APOEL',
        'Omonia',
        'HJK',
        'Vitória de Guimarães',
        'Astana',
        'Olimpija Ljubljana',
        'Cercle Brugge',
        'Shamrock Rovers',
        'The New Saints',
        'Lugano',
        'Heart of Midlothian',
        'Mladá Boleslav',
        'Petrocub Hîncești',
        'St. Gallen',
        'Panathinaikos',
        'TSC',
        'Borac Banja Luka',
        'Jagiellonia Białystok',
        'Celje',
        'Larne',
        'Dinamo Minsk',
        'Pafos',
        'Víkingur Reykjavík',
        'Noah',
        'St. Gallen',
        'Panathinaikos',
        'TSC',
        'Borac Banja Luka',
        'Jagiellonia Białystok',
        'Celje',
        'Larne',
        'Dinamo Minsk',
        'Pafos',
        'Víkingur Reykjavík',
        'Noah',
    ]

    const normalizedTitle = title
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .toLowerCase()

    const foundTeams: {team: string; index: number}[] = []

    for (const team of teams) {
        const normalizedTeam = team
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .toLowerCase()
        const escapedTeam = normalizedTeam.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
        const regex = new RegExp(`\\b${escapedTeam}\\b`)
        const match = normalizedTitle.match(regex)
        if (match && typeof match.index === 'number') {
            foundTeams.push({team, index: match.index})
        }
    }

    foundTeams.sort((a, b) => a.index - b.index)

    return foundTeams.length >= 2 ? [foundTeams[0].team, foundTeams[1].team] : []
}
*/

export function ExtractTeamsFromTitle(title: string): string[] {
    const normalizedTitle = title
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // remove accents
        .replace(/[’‘]/g, "'")
        .replace(/[\u2013\u2014]/g, '-') // normalize long dashes
        .toLowerCase()

    const separators = [' vs. ', ' vs ', ' v. ', ' v ', ' - ']
    let splitIndex = -1
    let separatorLength = 0

    // Find separator in normalized title
    for (const sep of separators) {
        const index = normalizedTitle.indexOf(sep)
        if (index !== -1) {
            splitIndex = index
            separatorLength = sep.length
            break
        }
    }

    if (splitIndex === -1) return []

    // Split original title using the found index
    const leftOriginal = title.slice(0, splitIndex)
    const rightOriginal = title.slice(splitIndex + separatorLength)

    const stopWords = new Set([
        'extended',
        'highlights',
        'match',
        'live',
        'preview',
        'reaction',
        'recap',
        'uel',
        'ucl',
        'epl',
        'serie',
        'bundesliga',
        'ligue',
        'mls',
        'la',
        'liga',
        'phase',
        'leg',
        'final',
        'quarter-final',
        'semi-final',
        'knockout',
        'group',
        'round',
        'play-off',
        'md',
        'el',
        'clasico',
        'premier',
        'league',
        '1',
        '2',
        'a',
    ])

    const cleanTeamName = (part: string): string => {
        const words = part.trim().split(/\s+/)
        const nameParts = []

        for (const word of words) {
            // Remove trailing punctuation but keep letters with accents and special characters
            const cleaned = word.replace(/[^\w\-/àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]+$/gi, '')
            if (!cleaned) break
            if (stopWords.has(cleaned.toLowerCase())) break
            // Allow letters (including accented), numbers, dots, hyphens, and slashes
            if (/^[\w.\-/àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]+$/i.test(cleaned)) {
                nameParts.push(cleaned.charAt(0).toUpperCase() + cleaned.slice(1))
            } else {
                break
            }
        }

        return nameParts.join(' ')
    }

    const teamA = cleanTeamName(leftOriginal)
    const teamB = cleanTeamName(rightOriginal)

    return teamA && teamB ? [teamA, teamB] : []
}

import {leagues, LeagueType} from '@/types/league'

export function leagueToDisplayName(league: string): string {
    const sportLeagues = leagues['soccer'] || []
    const leagueInfo = sportLeagues.find((l: LeagueType) => l.label === league)
    return leagueInfo?.displayName || league
}
