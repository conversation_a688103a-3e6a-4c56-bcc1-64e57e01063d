export function generateLabel(title: string) {
    return encodeURIComponent(
        title
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '') // Remove accents
            .trim()
            .toLowerCase()
            .replace(/\//g, '-') // Replace forward slashes with hyphens to avoid Next.js routing issues
            .replace(/\s+/g, '-') // Replace spaces with hyphens
    )
}

export function cleanYoutubeId(videoId: string) {
    return videoId.replace('-', '').replace('_', '')
}

export function ExtractTeamsFromTitle(title: string): string[] {
    const normalizedTitle = title
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // remove accents
        .replace(/[’‘]/g, "'")
        .replace(/[\u2013\u2014]/g, '-') // normalize long dashes
        .toLowerCase()

    const separators = [' vs. ', ' vs ', ' v. ', ' v ', ' - ']
    let splitIndex = -1
    let separatorLength = 0

    // Find separator in normalized title
    for (const sep of separators) {
        const index = normalizedTitle.indexOf(sep)
        if (index !== -1) {
            splitIndex = index
            separatorLength = sep.length
            break
        }
    }

    if (splitIndex === -1) return []

    // Split original title using the found index
    const leftOriginal = title.slice(0, splitIndex)
    const rightOriginal = title.slice(splitIndex + separatorLength)

    const stopWords = new Set([
        'extended',
        'highlights',
        'match',
        'live',
        'preview',
        'reaction',
        'recap',
        'uel',
        'ucl',
        'epl',
        'serie',
        'bundesliga',
        'ligue',
        'mls',
        'la',
        'liga',
        'phase',
        'leg',
        'final',
        'quarter-final',
        'semi-final',
        'knockout',
        'group',
        'round',
        'play-off',
        'md',
        'el',
        'clasico',
        'premier',
        'league',
        '1',
        '2',
        'a',
    ])

    const cleanTeamName = (part: string): string => {
        const words = part.trim().split(/\s+/)
        const nameParts = []

        for (const word of words) {
            // Remove trailing punctuation but keep letters with accents and special characters
            const cleaned = word.replace(/[^\w\-/\p{L}]+$/giu, '')
            if (!cleaned) break
            if (stopWords.has(cleaned.toLowerCase())) break
            // Allow letters (including accented), numbers, dots, hyphens, and slashes
            if (/^[\w.\-/\p{L}]+$/iu.test(cleaned)) {
                nameParts.push(cleaned.charAt(0).toUpperCase() + cleaned.slice(1))
            } else {
                break
            }
        }

        return nameParts.join(' ')
    }

    const teamA = cleanTeamName(leftOriginal)
    const teamB = cleanTeamName(rightOriginal)

    return teamA && teamB ? [teamA, teamB] : []
}

import {leagues, LeagueType} from '@/types/league'

export function leagueToDisplayName(league: string): string {
    const sportLeagues = leagues['soccer'] || []
    const leagueInfo = sportLeagues.find((l: LeagueType) => l.label === league)
    return leagueInfo?.displayName || league
}
