// Test script for ExtractTeamsFromTitle function

function ExtractTeamsFromTitle(title, debug = false) {
    const normalizedTitle = title
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // remove accents
        .replace(/['']/g, "'")
        .replace(/[\u2013\u2014]/g, '-') // normalize long dashes
        .toLowerCase()

    if (debug) {
        console.log(`Original title: "${title}"`)
        console.log(`Normalized title: "${normalizedTitle}"`)
    }

    const separators = [' vs. ', ' vs ', ' v. ', ' v ', ' - ']
    let splitIndex = -1
    let separatorLength = 0

    // Find separator in normalized title
    for (const sep of separators) {
        const index = normalizedTitle.indexOf(sep)
        if (index !== -1) {
            splitIndex = index
            separatorLength = sep.length
            if (debug) console.log(`Found separator "${sep}" at index ${index}`)
            break
        }
    }

    if (splitIndex === -1) {
        if (debug) console.log('No separator found!')
        return []
    }

    // Split original title using the found index
    const leftOriginal = title.slice(0, splitIndex)
    const rightOriginal = title.slice(splitIndex + separatorLength)

    if (debug) {
        console.log(`Left part: "${leftOriginal}"`)
        console.log(`Right part: "${rightOriginal}"`)
    }

    const stopWords = new Set([
        'extended',
        'highlights',
        'match',
        'live',
        'preview',
        'reaction',
        'recap',
        'uel',
        'ucl',
        'epl',
        'serie',
        'bundesliga',
        'ligue',
        'mls',
        'la',
        'liga',
        'phase',
        'leg',
        'final',
        'quarter-final',
        'semi-final',
        'knockout',
        'group',
        'round',
        'play-off',
        'md',
        'el',
        'clasico',
        'premier',
        'league',
        '1',
        '2',
        'a',
    ])

    const cleanTeamName = (part) => {
        const words = part.trim().split(/\s+/)
        const nameParts = []

        if (debug)
            console.log(
                `Cleaning part: "${part}" -> words: [${words.map((w) => `"${w}"`).join(', ')}]`
            )

        for (const word of words) {
            // Remove trailing punctuation but keep letters with accents and special characters
            const cleaned = word.replace(/[^\w\-/àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]+$/gi, '')
            if (debug) console.log(`  Word: "${word}" -> cleaned: "${cleaned}"`)

            if (!cleaned) {
                if (debug) console.log(`    Stopping: empty after cleaning`)
                break
            }
            if (stopWords.has(cleaned.toLowerCase())) {
                if (debug) console.log(`    Stopping: "${cleaned}" is a stop word`)
                break
            }
            // Allow letters (including accented), numbers, dots, hyphens, and slashes
            if (/^[\w.\-/àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]+$/i.test(cleaned)) {
                const capitalized = cleaned.charAt(0).toUpperCase() + cleaned.slice(1)
                nameParts.push(capitalized)
                if (debug) console.log(`    Added: "${capitalized}"`)
            } else {
                if (debug) console.log(`    Stopping: "${cleaned}" doesn't match regex`)
                break
            }
        }

        const result = nameParts.join(' ')
        if (debug) console.log(`  Final result: "${result}"`)
        return result
    }

    const teamA = cleanTeamName(leftOriginal)
    const teamB = cleanTeamName(rightOriginal)

    if (debug) {
        console.log(`Team A: "${teamA}"`)
        console.log(`Team B: "${teamB}"`)
    }

    return teamA && teamB ? [teamA, teamB] : []
}

// Test cases
const testTitles = [
    'Bodø/Glimt vs. Porto: Extended Highlights | UEL League Phase MD 1 | CBS Sports Golazo - Europe',
    'Ajax vs. Beşiktaş: Extended Highlights | UEL League Phase MD 1 | CBS Sports Golazo - Europe',
    'Beşiktaş vs. Athletic Club: Extended Highlights | UEL League Phase MD 7 | CBS Sports Golazo - Europe',
]

console.log('Testing ExtractTeamsFromTitle function:\n')

testTitles.forEach((title, index) => {
    console.log(`\n=== Test ${index + 1} ===`)
    console.log(`Title: "${title}"`)

    const result = ExtractTeamsFromTitle(title, true) // Enable debug mode
    console.log(`Result: [${result.map((team) => `"${team}"`).join(', ')}]`)

    if (result.length === 2) {
        console.log(`✅ Successfully extracted: ${result[0]} vs ${result[1]}`)
    } else {
        console.log(`❌ Failed to extract two teams`)
    }
    console.log('='.repeat(50))
})
