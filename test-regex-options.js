// Test different regex options for international characters

const testStrings = [
    "Beşiktaş",     // Turkish
    "Bodø/Glimt",   // Norwegian  
    "Ajax",         // Regular
    "Porto",        // Regular
    "Athletic",     // Regular
    "Malmö",        // Swedish
    "Zürich",       // German
    "Kraków",       // Polish
    "Atlético",     // Spanish
    "São Paulo",    // Portuguese
    "Ñublense",     // Spanish ñ
    "Čelik",        // Bosnian/Serbian
    "Žalgiris",     // Lithuanian
];

const regexOptions = [
    {
        name: "Current (limited accents)",
        regex: /^[\w.\-/àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]+$/i
    },
    {
        name: "Unicode Letters (\\p{L})",
        regex: /^[\w.\-/\p{L}]+$/iu
    },
    {
        name: "Latin Unicode ranges",
        regex: /^[\w.\-/\u0080-\u024F\u1E00-\u1EFF]+$/i
    },
    {
        name: "Detailed Latin blocks",
        regex: /^[\w.\-/\u00C0-\u00FF\u0100-\u017F\u0180-\u024F\u1E00-\u1EFF]+$/i
    }
];

console.log("Testing regex options for international team names:\n");

regexOptions.forEach((option, index) => {
    console.log(`${index + 1}. ${option.name}:`);
    console.log(`   Regex: ${option.regex}`);
    
    const results = testStrings.map(str => ({
        string: str,
        matches: option.regex.test(str)
    }));
    
    const passed = results.filter(r => r.matches).length;
    const failed = results.filter(r => !r.matches);
    
    console.log(`   Results: ${passed}/${testStrings.length} passed`);
    
    if (failed.length > 0) {
        console.log(`   Failed: ${failed.map(f => f.string).join(', ')}`);
    }
    
    console.log('');
});

// Test specifically with the problematic team names
console.log("=".repeat(50));
console.log("Testing with actual problematic cases:");

const problematicCases = ["Beşiktaş", "Bodø/Glimt"];

problematicCases.forEach(teamName => {
    console.log(`\nTesting "${teamName}":`);
    regexOptions.forEach((option, index) => {
        const matches = option.regex.test(teamName);
        const status = matches ? "✅ PASS" : "❌ FAIL";
        console.log(`  ${index + 1}. ${option.name}: ${status}`);
    });
});
